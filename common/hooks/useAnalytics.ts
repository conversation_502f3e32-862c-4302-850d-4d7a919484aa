'use client'

import { useCallback } from 'react';
import {
  analytics, AnalyticsEvent,
} from '@/common/utils/mixpanel';
import { Dict } from 'mixpanel-browser';

export const useAnalytics = () => {
  const track = useCallback((event: AnalyticsEvent | string, properties?: Dict) => {
    analytics.track(event, properties);
  }, []);

  const identify = useCallback((userId: string) => {
    analytics.identify(userId);
  }, []);

  const setUserProperties = useCallback((properties: Dict) => {
    analytics.setUserProperties(properties);
  }, []);

  const pageView = useCallback((pageName: string, properties?: Dict) => {
    analytics.pageView(pageName, properties);
  }, []);

  return {
    track,
    identify,
    setUserProperties,
    pageView,
    events: AnalyticsEvent,
  };
};
